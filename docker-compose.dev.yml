version: '3.8'

services:
  fastapi-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    env_file:
      - ./backend/.env
    volumes:
      # Enable volume mounting for development hot reload
      - ./backend:/app
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Development: Override CMD for hot reload
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  nextjs-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: deps  # Stop at deps stage for development
    ports:
      - "3000:3000"
    env_file:
      - ./frontend/.env.local  # Use local env for development
    volumes:
      # Enable volume mounting for development hot reload
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - fastapi-backend
    networks:
      - app-network
    restart: unless-stopped
    # Development: Override CMD for hot reload
    command: ["npm", "run", "dev"]
    environment:
      - NODE_ENV=development

networks:
  app-network:
    driver: bridge

volumes:
  backend_data:
    driver: local
