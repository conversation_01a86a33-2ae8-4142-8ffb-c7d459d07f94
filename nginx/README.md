# Nginx Configuration for SCDF Application

This directory contains the nginx configuration for the SCDF (Singapore Civil Defence Force) application deployment.

## Configuration Files

- `nginx.conf` - Main nginx configuration with performance optimizations
- `conf.d/default.conf` - Server block configuration with routing rules

## Port Configuration

The current configuration uses port 8080 instead of port 80 to avoid conflicts with existing services. To use port 80 in production:

1. Stop any services using port 80
2. Update `docker-compose.yml`:
   ```yaml
   nginx:
     ports:
       - "80:80"  # Change from "8080:80"
   ```
3. Update the websocket endpoint in `frontend/components/recording-screen.tsx`:
   ```typescript
   const WEBSOCKET_ENDPOINT = "ws://localhost/ws/rt-audio"  // Remove :8080
   ```
4. Update environment files and documentation accordingly

## Routing Rules

### Frontend Routes
- `/` - All frontend routes are proxied to the Next.js frontend service
- Static assets and pages are served through the frontend

### Backend API Routes
- `/api/*` - All API requests are proxied to the FastAPI backend service
- Removes the `/api` prefix when forwarding to backend

### WebSocket Routes
- `/ws/*` - WebSocket connections for real-time audio transcription
- Proper WebSocket headers and timeouts configured

### Health Check
- `/health` - Nginx health check endpoint (returns "healthy")

## Security Headers

The configuration includes security headers:
- X-Frame-Options: SAMEORIGIN
- X-XSS-Protection: 1; mode=block
- X-Content-Type-Options: nosniff
- Referrer-Policy: no-referrer-when-downgrade
- Content-Security-Policy: default-src 'self' http: https: data: blob: 'unsafe-inline'

## Performance Features

- Gzip compression enabled
- Long timeout values for WebSocket connections
- Proper proxy headers for real IP forwarding
- Connection upgrade support for WebSockets

## Testing

Test the configuration with:
```bash
# Health checks
curl http://localhost:8080/health
curl http://localhost:8080/api/health

# Frontend access
curl -I http://localhost:8080/

# Check container status
docker compose ps
```
