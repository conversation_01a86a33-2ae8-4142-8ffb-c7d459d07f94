version: '3.8'

services:
  # Nginx reverse proxy
  # nginx:
  #   image: nginx:latest
  #   container_name: nginx
  #   ports:
  #     - "80:80"
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./nginx/conf.d:/etc/nginx/conf.d:ro
  #   depends_on:
  #     - nextjs-frontend
  #     - fastapi-backend
  #   networks:
  #     - app-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "nginx", "-t"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 30s
  nginx:
    image: nginx:latest
    container_name: nginx
    restart: always
    volumes:
      # - ./nginx/nginx.conf:/etc/nginx/nginx.conf/:ro
      - ./certbot/conf:/etc/nginx/ssl/:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    ports:
      - 443:443
      - 80:80
    depends_on:
      - nextjs-frontend
      - fastapi-backend
    networks:
      - app-network

  certbot:
    image: certbot/certbot:latest
    container_name: certbot-init
    volumes:
      - ./certbot/conf:/etc/letsencrypt:rw
      - ./certbot/www:/var/www/certbot:rw

  fastapi-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    # Remove direct port exposure - traffic goes through nginx
    expose:
      - "8000"
    env_file:
      - ./backend/.env
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Remove volume mount for production - code is baked into image
    # volumes:
    #   - ./backend:/app

  nextjs-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    # Remove direct port exposure - traffic goes through nginx
    expose:
      - "3000"
    env_file:
      - ./frontend/.env
    depends_on:
      fastapi-backend:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  app-network:
    driver: bridge

volumes:
  backend_data: