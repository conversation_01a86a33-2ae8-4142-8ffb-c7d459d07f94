# WebSocket Recording Integration

This document explains how the real-time audio recording and transcription works in the FIU Witness Interview System.

## Overview

The recording screen now supports real-time audio transcription via WebSocket connection. Audio is captured from the user's microphone, processed, and sent as binary data to a WebSocket endpoint. The endpoint responds with transcription data that is displayed in real-time.

## Configuration

### Setting the WebSocket Endpoint

In `components/recording-screen.tsx`, update the `WEBSOCKET_ENDPOINT` constant:

```typescript
// TODO: Replace with your actual websocket endpoint
const WEBSOCKET_ENDPOINT = "ws://localhost:8080/transcribe" // Replace this with your actual endpoint
```

### WebSocket Protocol

#### Audio Input (Client → Server)
- **Format**: Binary data (Int16Array buffer)
- **Sample Rate**: 16kHz (configurable)
- **Channels**: 1 (mono)
- **Bit Depth**: 16-bit
- **Buffer Size**: 4096 samples (configurable)

#### Expected Response Format (Server → Client)

##### Transcription Response
```json
{
  "type": "transcription",
  "speaker": "witness" | "officer",
  "text": "The transcribed text from the audio",
  "timestamp": "MM:SS"
}
```

##### Partial Transcription (Optional)
```json
{
  "type": "partial_transcription",
  "text": "Partial transcription in progress...",
  "is_partial": true
}
```

##### Error Response
```json
{
  "type": "error",
  "message": "Description of the error that occurred"
}
```

## Features

### Real-time Transcription
- Audio is continuously captured and sent to the WebSocket endpoint
- Transcription results are displayed in real-time as they arrive
- Support for both final and partial transcription results

### Connection Management
- Automatic WebSocket connection on recording start
- Connection status indicator (connected/disconnected)
- Error handling and display
- Automatic reconnection attempts

### Audio Processing
- High-quality audio capture with noise suppression
- Configurable sample rate and buffer size
- Automatic gain control and echo cancellation
- Pause/resume functionality

### Speaker Detection
- Default speakers: "Officer" and "Witness"
- Color-coded speaker identification
- Configurable speaker names and colors

## Usage

### Starting a Recording
1. Navigate to the recording screen
2. Recording starts automatically when the component mounts
3. WebSocket connection is established
4. Audio capture begins and data is sent to the endpoint

### During Recording
- Real-time transcription appears in the transcript area
- Connection status is shown in the header
- Pause/resume controls are available
- Error messages are displayed if connection issues occur

### Stopping Recording
- Click "Stop Interview" to end the recording
- WebSocket connection is closed
- Navigate directly to transcription screen with captured data

## Implementation Details

### Custom Hook: `useWebSocketRecording`

The `useWebSocketRecording` hook manages:
- WebSocket connection lifecycle
- Audio capture and processing
- Transcription data state management
- Error handling

### Key Components

1. **WebSocket Management**
   - Connection establishment and cleanup
   - Message parsing and handling
   - Error recovery

2. **Audio Processing**
   - MediaDevices API for microphone access
   - AudioContext for audio processing
   - ScriptProcessorNode for real-time audio data

3. **State Management**
   - Transcript data accumulation
   - Connection status tracking
   - Error state handling

## Configuration Options

### WebSocket Recording Config
```typescript
interface WebSocketRecordingConfig {
  websocketUrl: string;           // WebSocket endpoint URL
  sampleRate?: number;            // Audio sample rate (default: 16000)
  bufferSize?: number;            // Audio buffer size (default: 4096)
  onTranscriptionUpdate?: (data: TranscriptData) => void;
  onError?: (error: string) => void;
  onConnectionChange?: (connected: boolean) => void;
}
```

### Audio Constraints
```typescript
{
  audio: {
    sampleRate: 16000,            // 16kHz sample rate
    channelCount: 1,              // Mono audio
    echoCancellation: true,       // Enable echo cancellation
    noiseSuppression: true,       // Enable noise suppression
    autoGainControl: true,        // Enable automatic gain control
  }
}
```

## Testing

### WebSocket Configuration Component

Use the `WebSocketConfig` component to test your endpoint:

```typescript
import { WebSocketConfig } from "@/components/websocket-config"

<WebSocketConfig 
  currentEndpoint={WEBSOCKET_ENDPOINT}
  onEndpointChange={(endpoint) => {
    // Update your endpoint configuration
  }}
/>
```

### Connection Testing
- Test WebSocket connectivity
- Verify response format
- Check error handling

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Verify WebSocket endpoint URL
   - Check if server is running
   - Ensure CORS is properly configured

2. **No Audio Data**
   - Check microphone permissions
   - Verify audio constraints
   - Test with different browsers

3. **Transcription Not Appearing**
   - Verify response format matches expected schema
   - Check browser console for parsing errors
   - Ensure WebSocket messages are properly formatted

4. **Performance Issues**
   - Adjust buffer size for your use case
   - Consider audio compression
   - Monitor WebSocket message frequency

### Browser Compatibility

- Chrome/Chromium: Full support
- Firefox: Full support
- Safari: Full support (with some limitations)
- Edge: Full support

### Security Considerations

- HTTPS required for microphone access in production
- WSS (secure WebSocket) recommended for production
- Proper CORS configuration needed
- Consider authentication for WebSocket endpoint

## Example Server Implementation

Here's a basic example of what your WebSocket server might look like:

```python
# Example Python WebSocket server (using websockets library)
import asyncio
import websockets
import json

async def handle_transcription(websocket, path):
    try:
        async for message in websocket:
            # Process binary audio data
            audio_data = message
            
            # Your transcription logic here
            transcription_result = process_audio(audio_data)
            
            # Send response
            response = {
                "type": "transcription",
                "speaker": "witness",  # or "officer"
                "text": transcription_result,
                "timestamp": get_current_timestamp()
            }
            
            await websocket.send(json.dumps(response))
            
    except Exception as e:
        error_response = {
            "type": "error",
            "message": str(e)
        }
        await websocket.send(json.dumps(error_response))

# Start server
start_server = websockets.serve(handle_transcription, "localhost", 8080)
asyncio.get_event_loop().run_until_complete(start_server)
asyncio.get_event_loop().run_forever()
```

This implementation provides a robust foundation for real-time audio transcription in the witness interview system.
