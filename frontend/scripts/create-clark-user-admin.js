// <PERSON>ript to create clark user with service role (bypasses RLS)
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://valqgfpmtmmqfzwhjowz.supabase.co';
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // You need to set this

if (!serviceRoleKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  console.log('Get this from your Supabase dashboard → Settings → API');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, serviceRoleKey);

async function createClarkUser() {
  console.log('👤 Creating clark user with admin privileges...');
  
  try {
    const { data: newUser, error } = await supabase
      .from('users')
      .insert([{
        email: '<EMAIL>',
        full_name: '<PERSON>',
        badge_number: 'CLARK001',
        department: 'Fire Investigation Unit',
        role: 'officer',
        is_active: true
      }])
      .select()
      .single();

    if (error) {
      console.error('❌ Error creating user:', error);
    } else {
      console.log('✅ Successfully created clark user:', {
        id: newUser.id,
        email: newUser.email,
        full_name: newUser.full_name,
        role: newUser.role
      });
    }
  } catch (error) {
    console.error('💥 Failed to create user:', error);
  }
}

createClarkUser();
