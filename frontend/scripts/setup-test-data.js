// <PERSON>ript to set up test data for the FIU Witness Interview application
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupTestData() {
  console.log('🚀 Setting up test data...');

  try {
    // Test connection
    console.log('📡 Testing database connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ Database connection failed:', connectionError.message);
      return;
    }

    console.log('✅ Database connection successful');

    // Create test users
    console.log('👥 Creating test users...');
    
    const testUsers = [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        email: '<EMAIL>',
        full_name: 'Detective Johnson',
        badge_number: 'FIU001',
        department: 'Fire Investigation Unit',
        role: 'officer',
        is_active: true
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        email: '<EMAIL>',
        full_name: 'Detective Smith',
        badge_number: 'FIU002',
        department: 'Fire Investigation Unit',
        role: 'officer',
        is_active: true
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        email: '<EMAIL>',
        full_name: 'Supervisor Brown',
        badge_number: 'FIU003',
        department: 'Fire Investigation Unit',
        role: 'supervisor',
        is_active: true
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        email: '<EMAIL>',
        full_name: 'System Administrator',
        badge_number: 'ADMIN001',
        department: 'Administration',
        role: 'admin',
        is_active: true
      }
    ];

    for (const user of testUsers) {
      const { error } = await supabase
        .from('users')
        .upsert(user, { onConflict: 'email' });

      if (error) {
        console.error(`❌ Failed to create user ${user.email}:`, error.message);
      } else {
        console.log(`✅ Created/updated user: ${user.email} (${user.role})`);
      }
    }

    // Create test cases
    console.log('📁 Creating test cases...');
    
    const testCases = [
      {
        id: 'FIU-2025-001',
        incident_location: '123 Main Street, Apartment 4B',
        incident_date: '2025-06-20',
        incident_time: '14:30',
        assigned_officer_id: '550e8400-e29b-41d4-a716-446655440001',
        status: 'In Progress'
      },
      {
        id: 'FIU-2025-002',
        incident_location: '456 Oak Avenue, Single Family Home',
        incident_date: '2025-06-18',
        incident_time: '09:15',
        assigned_officer_id: '550e8400-e29b-41d4-a716-446655440002',
        status: 'Completed'
      }
    ];

    for (const testCase of testCases) {
      const { error } = await supabase
        .from('cases')
        .upsert(testCase, { onConflict: 'id' });

      if (error) {
        console.error(`❌ Failed to create case ${testCase.id}:`, error.message);
      } else {
        console.log(`✅ Created/updated case: ${testCase.id}`);
      }
    }

    console.log('🎉 Test data setup completed successfully!');
    console.log('\n📋 Test Users Created:');
    console.log('- <EMAIL> (Officer)');
    console.log('- <EMAIL> (Officer)');
    console.log('- <EMAIL> (Supervisor)');
    console.log('- <EMAIL> (Admin)');
    console.log('\n🔐 To test login, you need to:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to Authentication > Users');
    console.log('3. Create auth users with the same email addresses');
    console.log('4. Or use the signup API endpoint');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

// Run the setup
setupTestData();
