// Script to test database connection and basic operations
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Required: NEXT_PUBLIC_SUPABASE_URL, NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDatabaseConnection() {
  console.log('🔗 Testing Supabase Database Connection\n');
  
  try {
    // Test 1: Basic connection
    console.log('1. Testing basic connection...');
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      console.log('❌ Connection failed:', error.message);
      return false;
    }
    console.log('✅ Database connection successful');

    // Test 2: Check if tables exist
    console.log('\n2. Checking database schema...');
    const tables = ['users', 'cases', 'interviews', 'transcriptions', 'statements', 'audio_recordings', 'export_logs'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ Table '${table}': ${error.message}`);
        } else {
          console.log(`✅ Table '${table}': Accessible`);
        }
      } catch (err) {
        console.log(`❌ Table '${table}': Error - ${err.message}`);
      }
    }

    // Test 3: Check for sample data
    console.log('\n3. Checking for sample data...');
    
    // Check users
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*');
    
    if (usersError) {
      console.log('❌ Users query failed:', usersError.message);
    } else {
      console.log(`✅ Users table: ${users.length} records found`);
      if (users.length > 0) {
        console.log(`   Sample user: ${users[0].email} (${users[0].role})`);
      }
    }

    // Check cases
    const { data: cases, error: casesError } = await supabase
      .from('cases')
      .select('*');
    
    if (casesError) {
      console.log('❌ Cases query failed:', casesError.message);
    } else {
      console.log(`✅ Cases table: ${cases.length} records found`);
      if (cases.length > 0) {
        console.log(`   Sample case: ${cases[0].id} - ${cases[0].incident_location}`);
      }
    }

    // Test 4: Test RLS (Row Level Security)
    console.log('\n4. Testing Row Level Security...');
    
    // This should fail without authentication
    const { data: protectedData, error: rlsError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>');
    
    if (rlsError) {
      console.log('✅ RLS is working - unauthorized access blocked');
    } else if (protectedData && protectedData.length === 0) {
      console.log('✅ RLS is working - no data returned without auth');
    } else {
      console.log('⚠️  RLS might not be configured properly');
    }

    console.log('\n📊 Database Integration Summary:');
    console.log('- Supabase connection: ✅ Working');
    console.log('- Database schema: ✅ Tables accessible');
    console.log('- Row Level Security: ✅ Configured');
    console.log('- Sample data: ' + (users.length > 0 ? '✅ Available' : '❌ Missing'));

    if (users.length === 0) {
      console.log('\n💡 Recommendations:');
      console.log('1. Run the database initialization script');
      console.log('2. Create sample users for testing');
      console.log('3. Set up authentication users in Supabase dashboard');
    }

    return true;

  } catch (error) {
    console.error('❌ Database test failed:', error);
    return false;
  }
}

// Run the test
testDatabaseConnection()
  .then(success => {
    if (success) {
      console.log('\n🎉 Database integration test completed successfully!');
    } else {
      console.log('\n💥 Database integration test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
