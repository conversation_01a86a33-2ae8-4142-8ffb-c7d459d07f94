"use client"

import { Provider } from 'jotai'
import { useAtom } from 'jotai'
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { StartScreen } from "@/components/start-screen"
import { CaseSelectionScreen } from "@/components/case-selection-screen"
import { NewCaseScreen } from "@/components/new-case-screen"
import { WitnessSetupScreen } from "@/components/witness-setup-screen"
import { RecordingScreen } from "@/components/recording-screen"
import { TranscriptionScreen } from "@/components/transcription-screen"
import { StatementEditScreen } from "@/components/statement-edit-screen"
import { ExportScreen } from "@/components/export-screen"
import { CaseHistoryScreen } from "@/components/case-history-screen"
import { LoadingScreen } from "@/components/loading-screen"
import { CaseDetailsScreen } from "@/components/case-detail-screen"
import { InterviewDetailScreen } from "@/components/interview-detail-screen"
import { currentScreenAtom } from "@/store/atoms"

export type Screen =
  | "start-screen"
  | "case-selection"
  | "new-case"
  | "witness-setup"
  | "recording-screen"
  | "transcription-screen"
  | "statement-edit"
  | "export-screen"
  | "case-history"
  | "case-details"
  | "interview-details"
  | "loading-screen"

function AppContent() {
  const [currentScreen, setCurrentScreen] = useAtom(currentScreenAtom)

  const navigateToScreen = (screen: Screen) => {
    setCurrentScreen(screen)
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case "start-screen":
        return <StartScreen onNavigate={navigateToScreen} />
      case "case-selection":
        return <CaseSelectionScreen onNavigate={navigateToScreen} />
      case "new-case":
        return <NewCaseScreen onNavigate={navigateToScreen} />
      case "witness-setup":
        return <WitnessSetupScreen onNavigate={navigateToScreen} />
      case "recording-screen":
        return <RecordingScreen onNavigate={navigateToScreen} />
      case "transcription-screen":
        return <TranscriptionScreen onNavigate={navigateToScreen} />
      case "statement-edit":
        return <StatementEditScreen onNavigate={navigateToScreen} />
      case "export-screen":
        return <ExportScreen onNavigate={navigateToScreen} />
      case "case-history":
        return <CaseHistoryScreen onNavigate={navigateToScreen} />
      case "case-details":
        return <CaseDetailsScreen onNavigate={navigateToScreen} />
      case "interview-details":
        return <InterviewDetailScreen onNavigate={navigateToScreen} />
      case "loading-screen":
        return <LoadingScreen onNavigate={navigateToScreen} />
      default:
        return <StartScreen onNavigate={navigateToScreen} />
    }
  }

  return (
    <div className="min-h-screen bg-background">{renderScreen()}</div>
  )
}

export default function Home() {
  return (
    <Provider>
      <ProtectedRoute>
        <AppContent />
      </ProtectedRoute>
    </Provider>
  )
}
