'use client'

import { useState } from 'react'
import { UserService, AuthService } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

export default function CreateUserProfilePage() {
  const [formData, setFormData] = useState({
    email: '',
    full_name: '',
    badge_number: '',
    department: 'Fire Investigation Unit',
    role: 'officer' as 'officer' | 'admin' | 'supervisor'
  })
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [loading, setLoading] = useState(false)
  const [currentUserInfo, setCurrentUserInfo] = useState<any>(null)

  const checkCurrentUser = async () => {
    try {
      const user = await AuthService.getCurrentUser()
      const profile = await AuthService.getCurrentUserProfile()
      setCurrentUserInfo({ user, profile })
      
      if (user?.email) {
        setFormData(prev => ({
          ...prev,
          email: user.email,
          full_name: user.user_metadata?.full_name || user.email.split('@')[0],
          badge_number: user.user_metadata?.badge_number || '',
          department: user.user_metadata?.department || 'Fire Investigation Unit',
          role: user.user_metadata?.role || 'officer'
        }))
      }
    } catch (err) {
      console.error('Error checking current user:', err)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setSuccess('')
    setLoading(true)

    try {
      // Check if user already exists
      const existingUser = await UserService.getUserByEmail(formData.email)
      if (existingUser) {
        setError('User profile already exists for this email')
        return
      }

      // Create user profile
      const newUser = await UserService.createUser(formData)
      setSuccess(`User profile created successfully for ${newUser.email}`)
      
      // Refresh current user info
      await checkCurrentUser()
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating user profile')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Create User Profile</CardTitle>
            <CardDescription>
              Create a user profile in the database for authentication
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={checkCurrentUser} variant="outline">
                Check Current User Status
              </Button>
              
              {currentUserInfo && (
                <div className="space-y-2">
                  <h3 className="font-semibold">Current User Info:</h3>
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                    {JSON.stringify(currentUserInfo, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Create User Profile</CardTitle>
            <CardDescription>
              Fill in the details to create a user profile
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              
              {success && (
                <Alert>
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  type="text"
                  value={formData.full_name}
                  onChange={(e) => handleInputChange('full_name', e.target.value)}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="badge_number">Badge Number</Label>
                <Input
                  id="badge_number"
                  type="text"
                  value={formData.badge_number}
                  onChange={(e) => handleInputChange('badge_number', e.target.value)}
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Input
                  id="department"
                  type="text"
                  value={formData.department}
                  onChange={(e) => handleInputChange('department', e.target.value)}
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="officer">Officer</SelectItem>
                    <SelectItem value="supervisor">Supervisor</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={loading}
              >
                {loading ? 'Creating Profile...' : 'Create User Profile'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
