import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a server-side client with service key
const getServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceKey = process.env.SUPABASE_SERVICE_KEY
  
  if (!supabaseUrl || !serviceKey) {
    throw new Error('Missing Supabase configuration for signature upload')
  }
  
  return createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

/**
 * Convert data URL to Blob
 */
function dataURLToBlob(dataURL: string): Blob {
  const arr = dataURL.split(',')
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png'
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  
  return new Blob([u8arr], { type: mime })
}

/**
 * Generate unique filename for signature
 */
function generateSignatureFilename(interviewId: string): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  return `signature_${interviewId}_${timestamp}.png`
}

export async function POST(request: NextRequest) {
  try {
    const { signatureDataURL, interviewId } = await request.json()
    
    if (!signatureDataURL || !interviewId) {
      return NextResponse.json(
        { error: 'Missing signatureDataURL or interviewId' },
        { status: 400 }
      )
    }

    const supabase = getServiceClient()
    const bucketName = process.env.SIGNATURE_BUCKET_NAME
    
    if (!bucketName) {
      return NextResponse.json(
        { error: 'SIGNATURE_BUCKET_NAME environment variable is not set' },
        { status: 500 }
      )
    }

    // Convert data URL to blob
    const blob = dataURLToBlob(signatureDataURL)
    
    // Generate unique filename
    const filename = generateSignatureFilename(interviewId)
    
    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(filename, blob, {
        contentType: 'image/png',
        upsert: false
      })

    if (error) {
      console.error('Supabase upload error:', error)
      return NextResponse.json(
        { error: `Failed to upload signature: ${error.message}` },
        { status: 500 }
      )
    }

    // Since bucket is private, we'll return the storage path instead of public URL
    // The frontend will use our API endpoints to access the image
    const storageUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${bucketName}/${filename}`

    return NextResponse.json({
      success: true,
      url: storageUrl
    })

  } catch (error) {
    console.error('Error uploading signature:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
