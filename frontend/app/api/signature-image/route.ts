import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a server-side client with service key
const getServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceKey = process.env.SUPABASE_SERVICE_KEY
  
  if (!supabaseUrl || !serviceKey) {
    throw new Error('Missing Supabase configuration')
  }
  
  return createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const signatureUrl = searchParams.get('url')
    
    if (!signatureUrl) {
      return NextResponse.json(
        { error: 'Missing signature URL' },
        { status: 400 }
      )
    }

    const supabase = getServiceClient()
    const bucketName = process.env.SIGNATURE_BUCKET_NAME
    
    if (!bucketName) {
      return NextResponse.json(
        { error: 'SIGNATURE_BUCKET_NAME environment variable is not set' },
        { status: 500 }
      )
    }

    // Extract the full path from URL (including folder structure for statement signatures)
    const url = new URL(signatureUrl)
    const pathParts = url.pathname.split('/').filter(part => part !== '')

    console.log('URL analysis:', {
      signatureUrl,
      pathname: url.pathname,
      pathParts,
      bucketName,
      expectedBucketName: process.env.SIGNATURE_BUCKET_NAME
    })

    // For Supabase storage URLs, the path is typically:
    // /storage/v1/object/public/{bucket_name}/{file_path}
    const storageIndex = pathParts.findIndex(part => part === 'storage')
    const publicIndex = pathParts.findIndex(part => part === 'public')

    if (storageIndex === -1 || publicIndex === -1) {
      console.error('Invalid Supabase storage URL format')
      return NextResponse.json(
        { error: 'Invalid signature URL format' },
        { status: 400 }
      )
    }

    // Get the bucket name from the URL (it's right after 'public')
    const urlBucketName = pathParts[publicIndex + 1]

    // Get the path within the bucket (everything after the bucket name)
    const pathInBucket = pathParts.slice(publicIndex + 2).join('/')

    console.log('Signature image request:', {
      signatureUrl,
      urlBucketName,
      pathInBucket,
      configuredBucketName: bucketName
    })

    // Use the bucket name from the URL instead of the configured one
    const actualBucketName = urlBucketName

    // Get signed URL for private access
    const { data, error } = await supabase.storage
      .from(actualBucketName)
      .createSignedUrl(pathInBucket, 3600) // 1 hour expiry

    if (error) {
      console.error('Error creating signed URL:', error)
      return NextResponse.json(
        { error: 'Failed to access signature image' },
        { status: 500 }
      )
    }

    if (!data?.signedUrl) {
      return NextResponse.json(
        { error: 'Failed to generate signed URL' },
        { status: 500 }
      )
    }

    // Fetch the image and return it as a blob
    const imageResponse = await fetch(data.signedUrl)
    
    if (!imageResponse.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch signature image' },
        { status: 500 }
      )
    }

    const imageBlob = await imageResponse.blob()
    
    return new NextResponse(imageBlob, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    })

  } catch (error) {
    console.error('Error fetching signature image:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
