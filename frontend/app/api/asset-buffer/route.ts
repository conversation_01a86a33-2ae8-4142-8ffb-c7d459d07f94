import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a server-side client with service key
const getServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceKey = process.env.SUPABASE_SERVICE_KEY
  
  if (!supabaseUrl || !serviceKey) {
    throw new Error('Missing Supabase configuration')
  }
  
  return createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const fileName = searchParams.get('fileName')
    
    if (!fileName) {
      return NextResponse.json(
        { error: 'Missing fileName parameter' },
        { status: 400 }
      )
    }

    const supabase = getServiceClient()
    const bucketName = process.env.ASSET_BUCKET_NAME
    
    if (!bucketName) {
      return NextResponse.json(
        { error: 'ASSET_BUCKET_NAME environment variable is not set' },
        { status: 500 }
      )
    }

    console.log('Asset buffer request:', {
      fileName,
      bucketName
    })

    // Download the file from Supabase storage
    const { data, error } = await supabase.storage
      .from(bucketName)
      .download(fileName)

    if (error) {
      console.error('Error downloading asset:', error)
      return NextResponse.json(
        { error: 'Failed to download asset' },
        { status: 500 }
      )
    }

    if (!data) {
      return NextResponse.json(
        { error: 'No data received for asset' },
        { status: 404 }
      )
    }

    // Convert blob to array buffer
    const arrayBuffer = await data.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    console.log(`Successfully loaded asset buffer: ${fileName}, size: ${buffer.length} bytes`)

    // Return the buffer as a binary response
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': data.type || 'image/png',
        'Content-Length': buffer.length.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    })

  } catch (error) {
    console.error('Error fetching asset buffer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
