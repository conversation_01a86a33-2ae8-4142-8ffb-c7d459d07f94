import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a server-side client with service key
const getServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceKey = process.env.SUPABASE_SERVICE_KEY
  
  if (!supabaseUrl || !serviceKey) {
    throw new Error('Missing Supabase configuration')
  }
  
  return createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const audioPath = searchParams.get('path')
    
    if (!audioPath) {
      return NextResponse.json(
        { error: 'Missing audio path' },
        { status: 400 }
      )
    }

    const supabase = getServiceClient()
    const bucketName = 'recording' // Audio files are stored in the 'recording' bucket
    
    console.log('Audio URL request:', {
      audioPath,
      bucketName
    })

    // Extract just the filename if the path contains a full URL
    let pathInBucket = audioPath

    // Check if it's a full URL (starts with http/https)
    if (audioPath.startsWith('http')) {
      try {
        const url = new URL(audioPath)
        const urlParts = url.pathname.split('/')
        const publicIndex = urlParts.findIndex(part => part === 'public')
        if (publicIndex !== -1 && urlParts.length > publicIndex + 2) {
          // Skip 'public' and bucket name, get the rest
          pathInBucket = urlParts.slice(publicIndex + 2).join('/')
        } else {
          // If no 'public' found, assume it's just a filename
          pathInBucket = urlParts[urlParts.length - 1]
        }
      } catch (error) {
        console.error('Error parsing URL:', error)
        // Fallback: try to extract filename from path
        pathInBucket = audioPath.split('/').pop() || audioPath
      }
    } else if (audioPath.includes('/')) {
      // If it contains slashes but isn't a full URL, extract the filename
      pathInBucket = audioPath.split('/').pop() || audioPath
    }
    // If it doesn't contain slashes, assume it's already just a filename

    console.log('Extracted path in bucket:', pathInBucket)

    // Get signed URL for private access
    const { data, error } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(pathInBucket, 3600) // 1 hour expiry

    if (error) {
      console.error('Error creating signed URL for audio:', error)
      return NextResponse.json(
        { error: 'Failed to access audio file' },
        { status: 500 }
      )
    }

    if (!data?.signedUrl) {
      return NextResponse.json(
        { error: 'Failed to generate signed URL' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      signedUrl: data.signedUrl
    })

  } catch (error) {
    console.error('Audio URL API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
