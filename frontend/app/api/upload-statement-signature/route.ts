import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client with service key for server-side operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_KEY!
)

// Helper function to convert data URL to blob
function dataURLToBlob(dataURL: string): Blob {
  const arr = dataURL.split(',')
  const mime = arr[0].match(/:(.*?);/)![1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  
  return new Blob([u8arr], { type: mime })
}

// Helper function to generate signature filename
function generateStatementSignatureFilename(interviewId: string, signatureType: string): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  return `${interviewId}/${signatureType}_signature_${timestamp}.png`
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { signatureDataURL, interviewId, signatureType } = body

    console.log('Statement signature upload request:', {
      interviewId,
      signatureType,
      hasSignatureData: !!signatureDataURL,
      signatureDataLength: signatureDataURL?.length
    })

    // Validate required fields
    if (!signatureDataURL || !interviewId || !signatureType) {
      console.error('Missing required fields:', { signatureDataURL: !!signatureDataURL, interviewId, signatureType })
      return NextResponse.json(
        { error: 'Missing required fields: signatureDataURL, interviewId, and signatureType are required' },
        { status: 400 }
      )
    }

    // Validate signature data URL format
    if (!signatureDataURL.startsWith('data:image/')) {
      return NextResponse.json(
        { error: 'Invalid signature data format' },
        { status: 400 }
      )
    }

    // Get bucket name from environment
    const bucketName = process.env.SIGNATURE_BUCKET_NAME
    if (!bucketName) {
      console.error('SIGNATURE_BUCKET_NAME environment variable not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    // Convert data URL to blob
    const blob = dataURLToBlob(signatureDataURL)
    
    // Generate unique filename with interview folder structure
    const filename = generateStatementSignatureFilename(interviewId, signatureType)
    
    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(filename, blob, {
        contentType: 'image/png',
        upsert: false
      })

    if (error) {
      console.error('Supabase upload error:', error)
      return NextResponse.json(
        { error: `Failed to upload signature: ${error.message}` },
        { status: 500 }
      )
    }

    if (!data?.path) {
      return NextResponse.json(
        { error: 'Upload succeeded but no path returned' },
        { status: 500 }
      )
    }

    // Generate the public URL for the uploaded signature
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(data.path)

    if (!urlData?.publicUrl) {
      console.error('Failed to generate public URL for uploaded signature')
      return NextResponse.json(
        { error: 'Failed to generate public URL' },
        { status: 500 }
      )
    }

    console.log('Statement signature uploaded successfully:', {
      interviewId,
      signatureType,
      path: data.path,
      url: urlData.publicUrl
    })

    return NextResponse.json({
      success: true,
      url: urlData.publicUrl,
      path: data.path
    })

  } catch (error) {
    console.error('Error in upload-statement-signature API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
