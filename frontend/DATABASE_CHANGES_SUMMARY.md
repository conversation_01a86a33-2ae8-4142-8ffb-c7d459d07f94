# Database Schema Changes Summary

## Overview
The database schema has been updated to simplify transcription storage and add proper user management. Here are the key changes made:

## Major Changes

### 1. Added User Management
- **New Table**: `users` - Stores officer/user information
- **Columns**: id, email, full_name, badge_number, department, role, is_active, timestamps
- **Purpose**: Proper authentication and user management instead of storing officer names as strings

### 2. Simplified Transcription Storage
- **Removed Tables**: `transcription_speakers` and `transcription_segments`
- **New Table**: `transcriptions` with JSONB storage
- **Purpose**: Store complete transcription as structured JSON instead of normalized segments

### 3. Updated Foreign Key Relationships
- **Cases**: Now reference `users.id` instead of storing officer email as string
- **Interviews**: Added `interviewing_officer_id` to track who conducted the interview
- **Export Logs**: Now reference `users.id` instead of storing names as strings

## Detailed Changes

### Schema Updates

#### New `users` Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    badge_number VARCHAR(50) UNIQUE,
    department VARCHAR(100),
    role user_role NOT NULL DEFAULT 'officer',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Updated `cases` Table
- **Changed**: `officer VARCHAR(255)` → `assigned_officer_id UUID REFERENCES users(id)`
- **Purpose**: Proper foreign key relationship to users

#### Updated `interviews` Table
- **Added**: `interviewing_officer_id UUID REFERENCES users(id)`
- **Purpose**: Track which officer conducted the interview

#### New `transcriptions` Table
```sql
CREATE TABLE transcriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) UNIQUE,
    transcription_data JSONB NOT NULL,
    language VARCHAR(10) DEFAULT 'en-US',
    confidence_score DECIMAL(3,2),
    processing_status transcription_status DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Updated `export_logs` Table
- **Changed**: `exported_by VARCHAR(255)` → `exported_by_user_id UUID REFERENCES users(id)`
- **Purpose**: Proper foreign key relationship to users

### JSONB Transcription Structure

The `transcription_data` JSONB field stores transcriptions in this format:

```json
{
  "speakers": [
    {
      "id": "officer",
      "name": "Detective Johnson",
      "type": "officer"
    },
    {
      "id": "witness", 
      "name": "Sarah Williams",
      "type": "witness"
    }
  ],
  "segments": [
    {
      "speaker": "officer",
      "timestamp": "00:00",
      "text": "Please state your name for the record.",
      "confidence": 0.95
    },
    {
      "speaker": "witness",
      "timestamp": "00:05", 
      "text": "My name is Sarah Williams.",
      "confidence": 0.98
    }
  ],
  "metadata": {
    "total_duration": 1800,
    "segment_count": 45,
    "average_confidence": 0.94
  }
}
```

### Benefits of Changes

1. **Simplified Speaker Management**: No need for separate speaker tables - speakers are derived from officer and witness names
2. **Better Performance**: Single JSONB field instead of multiple normalized tables for transcription data
3. **Proper User Management**: Full user authentication and authorization system
4. **Cleaner Relationships**: Proper foreign keys instead of string references
5. **Easier Frontend Integration**: JSONB structure matches frontend expectations

## Updated Files

### Database Files
- `database-schema.md` - Updated schema documentation
- `init-database.sql` - Updated initialization script with new structure

### TypeScript Files
- `types/database.ts` - Added User types, updated transcription types
- `lib/database-transformers.ts` - Updated transformers for new structure
- `lib/supabase.ts` - Added UserService, updated TranscriptionService

## Migration Notes

### For Existing Data
If you have existing data, you'll need to:

1. **Create users** from existing officer names in cases
2. **Migrate transcription data** from segments to JSONB format
3. **Update foreign key references** in cases and interviews

### Sample Migration Script
```sql
-- Create users from existing officer names
INSERT INTO users (email, full_name, role)
SELECT DISTINCT officer, officer, 'officer'
FROM cases
WHERE officer NOT IN (SELECT email FROM users);

-- Update cases to reference user IDs
UPDATE cases 
SET assigned_officer_id = users.id
FROM users 
WHERE cases.officer = users.email;
```

## API Changes

### New Endpoints Needed
- User management endpoints (CRUD operations)
- Updated transcription endpoints to work with JSONB

### Updated Request/Response Types
- All case operations now use user IDs instead of officer names
- Transcription data is now a single JSONB object
- Export operations reference user IDs

## Security Updates

### Row Level Security
- Users can only see their own data and colleagues in same department
- Cases and interviews are accessible based on assigned/interviewing officer
- Transcription access follows interview permissions

### Authentication
- JWT tokens should include user ID for RLS policies
- Role-based access control (officer, admin, supervisor)

## Next Steps

1. **Run the updated SQL script** to create the new schema
2. **Update your frontend** to use the new TypeScript types
3. **Test the new transcription storage** with JSONB format
4. **Implement user authentication** if not already done
5. **Update API endpoints** to match new schema structure

The new structure provides better data integrity, performance, and maintainability while simplifying the transcription storage model.
