"use client"

import { useEffect, useState } from "react"
import dynamic from "next/dynamic"

// Dynamically import the signature overlay to ensure it's only loaded on client
const SignatureOverlay = dynamic(() => import("./signature-overlay").then(mod => ({ default: mod.SignatureOverlay })), {
  ssr: false,
  loading: () => null
})

interface ClientSignatureOverlayProps {
  isOpen: boolean
  onClose: () => void
  onSave: (signatureData: string) => void
  existingSignature?: string
  title?: string
}

export function ClientSignatureOverlay(props: ClientSignatureOverlayProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Only render on client side to avoid hydration issues
  if (!isClient) {
    return null
  }

  return <SignatureOverlay {...props} />
}
