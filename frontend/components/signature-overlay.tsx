"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { X, RotateCcw, Check } from "lucide-react"
import SignatureCanvas from "react-signature-canvas"
import type ReactSignatureCanvas from "react-signature-canvas"

interface SignatureOverlayProps {
  isOpen: boolean
  onClose: () => void
  onSave: (signatureData: string) => void
  existingSignature?: string
  title?: string
}

export function SignatureOverlay({
  isOpen,
  onClose,
  onSave,
  existingSignature,
  title = "Witness Signature"
}: SignatureOverlayProps) {
  const signatureRef = useRef<ReactSignatureCanvas>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [hasSignature, setHasSignature] = useState(false)
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 400 })
  const [signatureData, setSignatureData] = useState<string | null>(null)

  useEffect(() => {
    if (isOpen && existingSignature && signatureRef.current) {
      // Store the signature data and load it
      setSignatureData(existingSignature)
      signatureRef.current.fromDataURL(existingSignature)
      setHasSignature(true)
    } else if (isOpen && !existingSignature) {
      // Clear signature data if no existing signature
      setSignatureData(null)
      setHasSignature(false)
    }
  }, [isOpen, existingSignature])

  // Handle canvas resizing
  useEffect(() => {
    const updateCanvasSize = () => {
      if (containerRef.current) {
        const container = containerRef.current
        const rect = container.getBoundingClientRect()
        const padding = 16 // Account for padding
        const newWidth = Math.max(300, rect.width - padding)
        const newHeight = Math.max(200, rect.height - padding)

        setCanvasSize({ width: newWidth, height: newHeight })

        // Resize the canvas if it exists
        if (signatureRef.current) {
          const canvas = signatureRef.current.getCanvas()
          if (canvas) {
            // Save current signature data before resize
            const currentSignature = signatureRef.current.isEmpty() ? null : signatureRef.current.toDataURL()

            canvas.width = newWidth
            canvas.height = newHeight

            // Restore signature after resize if it existed
            if (currentSignature || signatureData) {
              const dataToRestore = currentSignature || signatureData
              if (dataToRestore) {
                setTimeout(() => {
                  if (signatureRef.current) {
                    signatureRef.current.fromDataURL(dataToRestore)
                    setHasSignature(true)
                  }
                }, 50)
              }
            } else {
              signatureRef.current.clear()
            }
          }
        }
      }
    }

    if (isOpen) {
      // Initial size calculation
      setTimeout(updateCanvasSize, 100)

      // Add resize and orientation change listeners
      window.addEventListener('resize', updateCanvasSize)
      window.addEventListener('orientationchange', () => {
        setTimeout(updateCanvasSize, 200) // Delay for orientation change
      })

      return () => {
        window.removeEventListener('resize', updateCanvasSize)
        window.removeEventListener('orientationchange', updateCanvasSize)
      }
    }
  }, [isOpen])

  const handleClear = () => {
    if (signatureRef.current) {
      signatureRef.current.clear()
      setHasSignature(false)
      setSignatureData(null)
    }
  }

  const handleSave = () => {
    if (signatureRef.current && !signatureRef.current.isEmpty()) {
      const currentSignatureData = signatureRef.current.toDataURL()
      setSignatureData(currentSignatureData)
      onSave(currentSignatureData)
      onClose()
    }
  }

  const handleSignatureChange = () => {
    if (signatureRef.current) {
      const isEmpty = signatureRef.current.isEmpty()
      setHasSignature(!isEmpty)

      // Update stored signature data
      if (!isEmpty) {
        const currentSignatureData = signatureRef.current.toDataURL()
        setSignatureData(currentSignatureData)
      }
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-0 md:p-4">
      <Card className="w-full h-full md:w-full md:max-w-4xl md:h-auto md:max-h-[90vh] overflow-hidden md:rounded-lg">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 px-4 md:px-6">
          <CardTitle className="text-lg md:text-xl font-semibold">{title}</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="flex flex-col h-full space-y-3 md:space-y-4 px-4 md:px-6">
          <div className="text-sm md:text-base text-muted-foreground text-center md:text-left">
            Please sign in the area below to confirm your statement.
            <span className="block md:hidden text-xs mt-1 text-gray-500">
              Use your finger or stylus to sign
            </span>
          </div>

          {/* Signature Pad Container */}
          <div className="flex-1 border-2 border-dashed border-gray-300 rounded-lg p-2 md:p-4 bg-gray-50 min-h-0">
            <div
              ref={containerRef}
              className="w-full h-full min-h-[300px] md:min-h-[400px] flex items-center justify-center"
            >
              <SignatureCanvas
                ref={signatureRef}
                canvasProps={{
                  width: canvasSize.width,
                  height: canvasSize.height,
                  className: "signature-canvas bg-white rounded border max-w-full max-h-full touch-none",
                  style: { touchAction: 'none' }
                }}
                backgroundColor="rgb(255, 255, 255)"
                penColor="rgb(0, 0, 0)"
                minWidth={1}
                maxWidth={3}
                onEnd={handleSignatureChange}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3 pt-4 pb-2">
            <Button
              variant="outline"
              onClick={handleClear}
              className="flex items-center justify-center gap-2 order-2 sm:order-1"
              disabled={!hasSignature}
            >
              <RotateCcw className="h-4 w-4" />
              Clear
            </Button>

            <div className="flex gap-2 order-1 sm:order-2">
              <Button
                variant="outline"
                onClick={onClose}
                className="flex-1 sm:flex-none"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={!hasSignature}
                className="flex items-center justify-center gap-2 flex-1 sm:flex-none"
              >
                <Check className="h-4 w-4" />
                Save Signature
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
