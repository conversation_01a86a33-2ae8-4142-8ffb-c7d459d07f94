'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2, Play } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
  data?: any;
}

export function IntegrationTest() {
  const { user, isAuthenticated } = useAuth();
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Authentication', status: 'pending' },
    { name: 'Database Connection', status: 'pending' },
    { name: 'Supabase Services', status: 'pending' },
    { name: 'User Profile', status: 'pending' },
  ]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (name: string, status: TestResult['status'], message?: string, data?: any) => {
    setTests(prev => prev.map(test => 
      test.name === name ? { ...test, status, message, data } : test
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    // Reset all tests
    setTests(prev => prev.map(test => ({ ...test, status: 'pending' })));

    try {
      // Test 1: Authentication
      if (isAuthenticated && user) {
        updateTest('Authentication', 'success', `Logged in as ${user.fullName} (${user.role})`);
      } else {
        updateTest('Authentication', 'error', 'User not authenticated');
        setIsRunning(false);
        return;
      }

      // Test 2: User Profile
      updateTest('User Profile', 'success', `Role: ${user.role}, Department: ${user.department || 'N/A'}`);

      // Test 3: Supabase Services (Direct database access)
      try {
        const { CaseService, UserService } = await import('@/lib/supabase');

        // Test database connection with cases
        const cases = await CaseService.getCases({ limit: 1 });
        updateTest('Database Connection', 'success', `Connected - Found ${cases.length} cases`);

        // Test Supabase services
        const users = await UserService.getUsers();
        updateTest('Supabase Services', 'success',
          `Direct service access working. ${users.length} users, ${cases.length} cases.`,
          { usersCount: users.length, casesCount: cases.length }
        );
      } catch (error) {
        updateTest('Supabase Services', 'error', `Service error: ${error instanceof Error ? error.message : 'Unknown'}`);
        updateTest('Database Connection', 'error', 'Failed to connect to database');
      }

    } catch (error) {
      console.error('Test execution error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">Pass</Badge>;
      case 'error':
        return <Badge variant="destructive">Fail</Badge>;
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5" />
          Integration Test Suite
        </CardTitle>
        <CardDescription>
          Verify that authentication, database, and API integration are working correctly
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isAuthenticated && (
          <Alert>
            <AlertDescription>
              Please log in to run the integration tests.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex justify-between items-center">
          <Button 
            onClick={runTests} 
            disabled={!isAuthenticated || isRunning}
            className="flex items-center gap-2"
          >
            {isRunning ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <Play className="h-4 w-4" />
                Run Tests
              </>
            )}
          </Button>
          
          {user && (
            <div className="text-sm text-gray-600">
              Testing as: <strong>{user.fullName}</strong> ({user.role})
            </div>
          )}
        </div>

        <div className="space-y-3">
          {tests.map((test) => (
            <div key={test.name} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(test.status)}
                <div>
                  <div className="font-medium">{test.name}</div>
                  {test.message && (
                    <div className="text-sm text-gray-600">{test.message}</div>
                  )}
                </div>
              </div>
              {getStatusBadge(test.status)}
            </div>
          ))}
        </div>

        {tests.some(test => test.status === 'success') && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Integration tests completed! The system is properly configured and working.
            </AlertDescription>
          </Alert>
        )}

        {tests.some(test => test.status === 'error') && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              Some tests failed. Please check your configuration and try again.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}

export default IntegrationTest;
