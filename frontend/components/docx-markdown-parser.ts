import { Paragraph, TextRun } from "docx"

interface MarkdownElement {
  type: "h1" | "h2" | "h3" | "paragraph" | "list" | "listItem"
  content: string
  children?: MarkdownElement[]
}

// Parse markdown text into structured elements
const parseMarkdown = (text: string): MarkdownElement[] => {
  const lines = text.split("\n").filter((line) => line.trim() !== "")
  const elements: MarkdownElement[] = []
  let currentList: MarkdownElement | null = null

  for (const line of lines) {
    const trimmedLine = line.trim()

    if (trimmedLine.startsWith("# ")) {
      // H1 header
      if (currentList) {
        elements.push(currentList)
        currentList = null
      }
      elements.push({
        type: "h1",
        content: trimmedLine.substring(2).trim(),
      })
    } else if (trimmedLine.startsWith("## ")) {
      // H2 header
      if (currentList) {
        elements.push(currentList)
        currentList = null
      }
      elements.push({
        type: "h2",
        content: trimmedLine.substring(3).trim(),
      })
    } else if (trimmedLine.startsWith("### ")) {
      // H3 header
      if (currentList) {
        elements.push(currentList)
        currentList = null
      }
      elements.push({
        type: "h3",
        content: trimmedLine.substring(4).trim(),
      })
    } else if (trimmedLine.startsWith("- ")) {
      // List item
      const listItem: MarkdownElement = {
        type: "listItem",
        content: trimmedLine.substring(2).trim(),
      }

      if (!currentList) {
        currentList = {
          type: "list",
          content: "",
          children: [],
        }
      }
      currentList.children!.push(listItem)
    } else if (trimmedLine !== "") {
      // Regular paragraph
      if (currentList) {
        elements.push(currentList)
        currentList = null
      }
      elements.push({
        type: "paragraph",
        content: trimmedLine,
      })
    }
  }

  // Don't forget to add the last list if it exists
  if (currentList) {
    elements.push(currentList)
  }

  return elements
}

// Parse text with bold formatting and return TextRun array with consistent font styling
const parseTextWithBold = (text: string, options: { size?: number; bold?: boolean } = {}): TextRun[] => {
  const parts = text.split(/(\*\*.*?\*\*)/g)
  const textRuns: TextRun[] = []
  const defaultSize = options.size || 20 // 10pt default
  const isHeading = options.bold || false

  for (const part of parts) {
    if (part.startsWith("**") && part.endsWith("**")) {
      // Bold text
      textRuns.push(
        new TextRun({
          text: part.slice(2, -2),
          color: "000000",
          bold: true,
          font: "Times New Roman",
          size: defaultSize,
        }),
      )
    } else if (part.trim() !== "") {
      // Regular text
      textRuns.push(
        new TextRun({
          text: part,
          color: "000000",
          bold: isHeading,
          font: "Times New Roman",
          size: defaultSize,
        }),
      )
    }
  }

  return textRuns
}

// Convert markdown elements to DOCX paragraphs
export const convertMarkdownToDOCX = (markdownText: string): Paragraph[] => {
  const elements = parseMarkdown(markdownText)
  const paragraphs: Paragraph[] = []

  for (const element of elements) {
    switch (element.type) {
      case "h1":
        paragraphs.push(
          new Paragraph({
            children: parseTextWithBold(element.content, { size: 24, bold: true }), // 12pt
            spacing: {
              before: 240, // 12pt
              after: 120, // 6pt
            },
          }),
        )
        break

      case "h2":
        paragraphs.push(
          new Paragraph({
            children: parseTextWithBold(element.content, { size: 22, bold: true }), // 11pt
            spacing: {
              before: 200, // 10pt
              after: 100, // 5pt
            },
          }),
        )
        break

      case "h3":
        paragraphs.push(
          new Paragraph({
            children: parseTextWithBold(element.content, { size: 20, bold: true }), // 10pt
            spacing: {
              before: 160, // 8pt
              after: 80, // 4pt
            },
          }),
        )
        break

      case "paragraph":
        paragraphs.push(
          new Paragraph({
            children: parseTextWithBold(element.content), // Uses default 20pt (10pt) size
            spacing: {
              after: 120, // 6pt
            },
          }),
        )
        break

      case "list":
        // Add each list item as a separate paragraph with bullet formatting
        if (element.children) {
          for (const listItem of element.children) {
            paragraphs.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: "• ", // Bullet character
                    font: "Times New Roman",
                    size: 20, // 10pt
                  }),
                  ...parseTextWithBold(listItem.content), // Uses default 20pt (10pt) size
                ],
                spacing: {
                  after: 80, // 4pt
                },
                indent: {
                  left: 360, // 0.25 inch indent
                },
              }),
            )
          }
        }
        break
    }
  }

  return paragraphs
}
