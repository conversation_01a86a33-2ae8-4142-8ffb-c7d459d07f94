import type React from "react"
import { Text, View, StyleSheet } from "@react-pdf/renderer"

// Styles for markdown elements in PDF
const markdownStyles = StyleSheet.create({
  h1: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
    marginTop: 12,
    color: "#000000",
  },
  h2: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 6,
    marginTop: 10,
    color: "#000000",
  },
  h3: {
    fontSize: 12,
    fontWeight: "bold",
    marginBottom: 4,
    marginTop: 8,
    color: "#000000",
  },
  paragraph: {
    fontSize: 11,
    marginBottom: 6,
    lineHeight: 1.4,
    color: "#000000",
  },
  bold: {
    fontWeight: "bold",
  },
  listContainer: {
    marginBottom: 8,
    marginLeft: 15,
  },
  listItem: {
    fontSize: 11,
    marginBottom: 3,
    lineHeight: 1.4,
    color: "#000000",
  },
  listBullet: {
    position: "absolute",
    left: -10,
  },
})

interface MarkdownElement {
  type: "h1" | "h2" | "h3" | "paragraph" | "list" | "listItem"
  content: string
  children?: MarkdownElement[]
}

// Parse markdown text into structured elements
const parseMarkdown = (text: string): MarkdownElement[] => {
  const lines = text.split("\n").filter((line) => line.trim() !== "")
  const elements: MarkdownElement[] = []
  let currentList: MarkdownElement | null = null

  for (const line of lines) {
    const trimmedLine = line.trim()

    if (trimmedLine.startsWith("# ")) {
      // H1 header
      if (currentList) {
        elements.push(currentList)
        currentList = null
      }
      elements.push({
        type: "h1",
        content: trimmedLine.substring(2).trim(),
      })
    } else if (trimmedLine.startsWith("## ")) {
      // H2 header
      if (currentList) {
        elements.push(currentList)
        currentList = null
      }
      elements.push({
        type: "h2",
        content: trimmedLine.substring(3).trim(),
      })
    } else if (trimmedLine.startsWith("### ")) {
      // H3 header
      if (currentList) {
        elements.push(currentList)
        currentList = null
      }
      elements.push({
        type: "h3",
        content: trimmedLine.substring(4).trim(),
      })
    } else if (trimmedLine.startsWith("- ")) {
      // List item
      const listItem: MarkdownElement = {
        type: "listItem",
        content: trimmedLine.substring(2).trim(),
      }

      if (!currentList) {
        currentList = {
          type: "list",
          content: "",
          children: [],
        }
      }
      currentList.children!.push(listItem)
    } else if (trimmedLine !== "") {
      // Regular paragraph
      if (currentList) {
        elements.push(currentList)
        currentList = null
      }
      elements.push({
        type: "paragraph",
        content: trimmedLine,
      })
    }
  }

  // Don't forget to add the last list if it exists
  if (currentList) {
    elements.push(currentList)
  }

  return elements
}

// Render text with bold formatting
const renderTextWithBold = (text: string) => {
  const parts = text.split(/(\*\*.*?\*\*)/g)

  return parts.map((part, index) => {
    if (part.startsWith("**") && part.endsWith("**")) {
      // Bold text
      return (
        <Text key={index} style={markdownStyles.bold}>
          {part.slice(2, -2)}
        </Text>
      )
    } else {
      // Regular text
      return part
    }
  })
}

// Main component to render markdown in PDF
interface PDFMarkdownProps {
  content: string
  containerStyle?: any
}

export const PDFMarkdown: React.FC<PDFMarkdownProps> = ({ content, containerStyle }) => {
  const elements = parseMarkdown(content)

  return (
    <View style={containerStyle}>
      {elements.map((element, index) => {
        switch (element.type) {
          case "h1":
            return (
              <Text key={index} style={markdownStyles.h1}>
                {renderTextWithBold(element.content)}
              </Text>
            )
          case "h2":
            return (
              <Text key={index} style={markdownStyles.h2}>
                {renderTextWithBold(element.content)}
              </Text>
            )
          case "h3":
            return (
              <Text key={index} style={markdownStyles.h3}>
                {renderTextWithBold(element.content)}
              </Text>
            )
          case "paragraph":
            return (
              <Text key={index} style={markdownStyles.paragraph}>
                {renderTextWithBold(element.content)}
              </Text>
            )
          case "list":
            return (
              <View key={index} style={markdownStyles.listContainer}>
                {element.children?.map((listItem, listIndex) => (
                  <View key={listIndex} style={{ position: "relative" }}>
                    <Text style={markdownStyles.listBullet}>•</Text>
                    <Text style={markdownStyles.listItem}>{renderTextWithBold(listItem.content)}</Text>
                  </View>
                ))}
              </View>
            )
          default:
            return null
        }
      })}
    </View>
  )
}
