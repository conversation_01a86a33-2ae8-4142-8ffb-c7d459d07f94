"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useAtom } from 'jotai'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft } from "lucide-react"
import { currentCaseAtom, currentWitnessAtom, summaryDataAtom } from "@/store/atoms"
import type { Screen } from "@/app/page"

interface StatementEditScreenProps {
  onNavigate: (screen: Screen) => void
}

export function StatementEditScreen({ onNavigate }: StatementEditScreenProps) {
  const [currentCase] = useAtom(currentCaseAtom)
  const [currentWitness] = useAtom(currentWitnessAtom)
  const [summaryData, setSummaryData] = useAtom(summaryDataAtom)

  const [formData, setFormData] = useState({
    caseInfo: "",
    witnessInfo: "",
    statement: "",
  })

  useEffect(() => {
    if (currentCase && currentWitness) {
      setFormData({
        caseInfo: `${currentCase.id} - ${currentCase.incidentLocation}`,
        witnessInfo: `${currentWitness.name} (${currentWitness.type})`,
        statement: summaryData || "",
      })
    }
  }, [currentCase, currentWitness, summaryData])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setSummaryData(formData.statement)
    onNavigate("transcription-screen")
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("transcription-screen")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">Edit Statement</h2>
        <div></div>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="case-info">Case Information</Label>
              <Input id="case-info" value={formData.caseInfo} readOnly className="bg-muted" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="witness-info">Witness Information</Label>
              <Input id="witness-info" value={formData.witnessInfo} readOnly className="bg-muted" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="statement">Statement Summary</Label>
              <Textarea
                id="statement"
                value={formData.statement}
                onChange={(e) => handleInputChange("statement", e.target.value)}
                rows={12}
                className="min-h-[250px]"
                placeholder="Edit the auto-generated statement summary..."
              />
              <p className="text-sm text-muted-foreground">
                Note: Officer notes can be added in the transcription screen before approving the interview.
              </p>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="secondary"
                className="flex-1"
                onClick={() => onNavigate("transcription-screen")}
              >
                Cancel
              </Button>
              <Button type="submit" className="flex-1">
                Save Changes
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
