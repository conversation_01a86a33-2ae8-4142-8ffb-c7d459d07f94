'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useEffect } from 'react'

export function AuthStatus() {
  const { user, loading, isAuthenticated } = useAuth()

  useEffect(() => {
    console.log('Auth Status Update:', {
      user: user ? { id: user.id, email: user.email, role: user.role } : null,
      loading,
      isAuthenticated,
      timestamp: new Date().toISOString()
    })
  }, [user, loading, isAuthenticated])

  if (loading) {
    return (
      <div className="fixed top-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-2 rounded">
        🔄 Loading authentication...
      </div>
    )
  }

  if (isAuthenticated && user) {
    return (
      <div className="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded">
        ✅ Authenticated as {user.fullName}
      </div>
    )
  }

  return (
    <div className="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded">
      ❌ Not authenticated
    </div>
  )
}
