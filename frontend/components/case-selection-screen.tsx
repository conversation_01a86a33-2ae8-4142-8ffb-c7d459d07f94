"use client"

import { use<PERSON>tom } from 'jotai'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Loader2 } from "lucide-react"
import { useCases } from "@/hooks/use-cases"
import { currentCaseAtom } from "@/store/atoms"
import type { Screen } from "@/app/page"
import type { Case } from "@/types/database"

interface CaseSelectionScreenProps {
  onNavigate: (screen: Screen) => void
}

export function CaseSelectionScreen({ onNavigate }: CaseSelectionScreenProps) {
  const { cases, loading, error, refetch } = useCases();
  const [, setCurrentCase] = useAtom(currentCaseAtom)

  const selectCase = (selectedCase: Case) => {
    setCurrentCase(selectedCase)
    onNavigate("witness-setup")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(":")
    const hour = Number.parseInt(hours)
    const ampm = hour >= 12 ? "PM" : "AM"
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes} ${ampm}`
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("start-screen")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">Select Case</h2>
        <div></div>
      </div>

      <div className="space-y-4 mb-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>Loading cases...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Error loading cases: {error}</p>
            <Button variant="outline" onClick={refetch}>
              Try Again
            </Button>
          </div>
        ) : cases.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">No cases found</p>
            <Button variant="outline" onClick={refetch}>
              Refresh
            </Button>
          </div>
        ) : (
          cases.map((caseItem) => (
            <Card
              key={caseItem.id}
              className="cursor-pointer hover:shadow-md transition-all duration-200 hover:-translate-y-1"
              onClick={() => selectCase(caseItem)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-primary">{caseItem.id}</h4>
                  <Badge variant={caseItem.status === "In Progress" ? "secondary" : "default"}>{caseItem.status}</Badge>
                </div>
                <div className="space-y-1 text-sm">
                  <p>
                    <strong>Location:</strong> {caseItem.incidentLocation}
                  </p>
                  <p>
                    <strong>Date:</strong> {formatDate(caseItem.incidentDate)} at {formatTime(caseItem.incidentTime)}
                  </p>
                  <p>
                    <strong>Officer:</strong> {caseItem.assignedOfficer?.fullName || 'Unknown'}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <Button variant="secondary" className="w-full" onClick={() => onNavigate("new-case")}>
        Create New Case
      </Button>
    </div>
  )
}
