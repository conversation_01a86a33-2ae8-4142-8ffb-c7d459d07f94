# Frontend Environment Variables Example
# Copy this file to .env.local for development or .env for production

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# Bucket Names
SIGNATURE_BUCKET_NAME=witness-signatures
ASSET_BUCKET_NAME=assets

# Backend API URL
# For development: http://localhost:8000 (direct backend access)
# For production with nginx: http://localhost:8080/api (through nginx proxy)
# For Docker without nginx: http://fastapi-backend:8000
NEXT_PUBLIC_API_URL=http://localhost:8080/api

# Production Settings (optional)
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
