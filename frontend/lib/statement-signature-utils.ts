import type { StatementFormData } from "@/types/database"

/**
 * Utility functions for handling statement form signatures in exports
 */

export interface StatementFormSignatures {
  interpreterSignature?: string
  recordingOfficerSignature?: string
  statementMakerSignature?: string
  scdfInterviewerSignature?: string
}

/**
 * Fetch and convert a single signature URL to base64 for embedding in exports
 */
export const fetchSignatureAsBase64 = async (signatureUrl: string): Promise<string | null> => {
  try {
    // If it's already a base64 data URL, return as is
    if (signatureUrl.startsWith('data:image/')) {
      return signatureUrl
    }

    // Fetch via API route to get base64
    const response = await fetch(`/frontend-api/signature-base64?url=${encodeURIComponent(signatureUrl)}`)
    
    if (!response.ok) {
      console.error('Failed to fetch signature:', response.statusText)
      return null
    }

    const data = await response.json()
    
    if (data.success && data.base64) {
      return data.base64
    }

    console.error('Invalid response from signature API:', data)
    return null
  } catch (error) {
    console.error('Error fetching signature as base64:', error)
    return null
  }
}

/**
 * Fetch all statement form signatures and convert them to base64 for export
 */
export const fetchStatementFormSignatures = async (
  statementForm: StatementFormData
): Promise<StatementFormSignatures> => {
  const signatures: StatementFormSignatures = {}

  // Define signature fields to process
  const signatureFields = [
    { field: 'interpreterSignature', key: 'interpreterSignature' as keyof StatementFormSignatures },
    { field: 'recordingOfficerSignature', key: 'recordingOfficerSignature' as keyof StatementFormSignatures },
    { field: 'statementMakerSignature', key: 'statementMakerSignature' as keyof StatementFormSignatures },
    { field: 'scdfInterviewerSignature', key: 'scdfInterviewerSignature' as keyof StatementFormSignatures }
  ] as const

  // Process each signature field
  await Promise.all(
    signatureFields.map(async ({ field, key }) => {
      const signatureUrl = statementForm[field]
      if (signatureUrl) {
        const base64Signature = await fetchSignatureAsBase64(signatureUrl)
        if (base64Signature) {
          signatures[key] = base64Signature
        }
      }
    })
  )

  return signatures
}

/**
 * Check if a signature URL is valid and accessible
 */
export const isValidSignatureUrl = (url?: string): boolean => {
  if (!url) return false
  
  // Check if it's a data URL (base64)
  if (url.startsWith('data:image/')) {
    return true
  }
  
  // Check if it's a valid HTTP/HTTPS URL
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

/**
 * Get signature display name for UI
 */
export const getSignatureDisplayName = (signatureType: keyof StatementFormSignatures): string => {
  const displayNames = {
    interpreterSignature: 'Interpreter Signature',
    recordingOfficerSignature: 'Recording Officer Signature',
    statementMakerSignature: 'Statement Maker Signature',
    scdfInterviewerSignature: 'SCDF Interviewer Signature'
  }
  
  return displayNames[signatureType] || 'Unknown Signature'
}
