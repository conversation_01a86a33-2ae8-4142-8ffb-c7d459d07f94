// Signature upload service using API routes
export class SignatureService {
  /**
   * Upload signature to Supabase storage via API route
   */
  static async uploadSignature(
    signatureDataURL: string,
    interviewId: string
  ): Promise<string> {
    try {
      const response = await fetch('/api/upload-signature', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          signatureDataURL,
          interviewId
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to upload signature')
      }

      const data = await response.json()

      if (!data.success || !data.url) {
        throw new Error('Invalid response from signature upload API')
      }

      return data.url
    } catch (error) {
      console.error('Error uploading signature:', error)
      throw error
    }
  }

  /**
   * Upload statement form signature to Supabase storage via API route
   */
  static async uploadStatementSignature(
    signatureDataURL: string,
    interviewId: string,
    signatureType: string
  ): Promise<string> {
    try {
      const response = await fetch('/api/upload-statement-signature', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          signatureDataURL,
          interviewId,
          signatureType
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to upload signature')
      }

      const data = await response.json()

      if (!data.success || !data.url) {
        throw new Error('Invalid response from signature upload API')
      }

      return data.url
    } catch (error) {
      console.error('Error uploading statement signature:', error)
      throw error
    }
  }

  /**
   * Validate signature data URL
   */
  static validateSignatureData(dataURL: string): boolean {
    try {
      // Check if it's a valid data URL
      if (!dataURL.startsWith('data:image/')) {
        return false
      }

      // Check if it has base64 data
      const parts = dataURL.split(',')
      if (parts.length !== 2) {
        return false
      }

      // Try to decode base64
      atob(parts[1])
      return true
    } catch {
      return false
    }
  }
}
