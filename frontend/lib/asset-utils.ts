/**
 * Fetch an asset from Supabase bucket and convert to base64 for embedding in exports
 * Uses API route to handle server-side Supabase access
 */
export const fetchAssetAsBase64 = async (fileName: string): Promise<string | null> => {
  try {
    console.log(`Fetching asset: ${fileName}`)

    const response = await fetch(`/frontend-api/asset-base64?fileName=${encodeURIComponent(fileName)}`)

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Error fetching asset:', errorData.error)
      return null
    }

    const data = await response.json()

    if (!data.success || !data.base64) {
      console.error('Invalid response from asset API')
      return null
    }

    console.log(`Successfully loaded asset: ${fileName}, size: ${data.size} bytes`)
    return data.base64
  } catch (error) {
    console.error('Error fetching asset as base64:', error)
    return null
  }
}

/**
 * Fetch an asset from Supabase bucket and return as Buffer for DOCX embedding
 * Uses API route to handle server-side Supabase access
 */
export const fetchAssetAsBuffer = async (fileName: string): Promise<Buffer | null> => {
  try {
    console.log(`Fetching asset as buffer: ${fileName}`)

    const response = await fetch(`/frontend-api/asset-buffer?fileName=${encodeURIComponent(fileName)}`)

    if (!response.ok) {
      console.error('Error fetching asset buffer:', response.statusText)
      return null
    }

    const arrayBuffer = await response.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    console.log(`Successfully loaded asset buffer: ${fileName}, size: ${buffer.length} bytes`)
    return buffer
  } catch (error) {
    console.error('Error fetching asset as buffer:', error)
    return null
  }
}

/**
 * Check if an asset exists by trying to fetch it
 */
export const assetExists = async (fileName: string): Promise<boolean> => {
  try {
    const response = await fetch(`/frontend-api/asset-base64?fileName=${encodeURIComponent(fileName)}`)
    return response.ok
  } catch (error) {
    console.error('Error checking asset existence:', error)
    return false
  }
}
