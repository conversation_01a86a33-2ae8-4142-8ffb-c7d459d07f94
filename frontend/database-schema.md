# FIU Witness Interview Database Schema

## Overview
This database schema is designed for PostgreSQL on Supabase to support the Fire Investigation Unit witness interview management system.

## Tables

### 1. users
Stores officer/user information for authentication and authorization.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique user identifier |
| email | VARCHAR(255) | UNIQUE, NOT NULL | Officer email address |
| full_name | VARCHAR(255) | NOT NULL | Officer full name |
| badge_number | VARCHAR(50) | UNIQUE | Officer badge number |
| department | VARCHAR(100) | | Department/unit |
| role | VARCHAR(20) | NOT NULL, CHECK | User role (officer, admin, supervisor) |
| is_active | BOOLEAN | DEFAULT TRUE | Account status |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Account creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

### 2. cases
Stores fire investigation case information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | VARCHAR(50) | PRIMARY KEY | Unique case identifier (e.g., FIU-2025-001) |
| incident_location | TEXT | NOT NULL | Location where the incident occurred |
| incident_date | DATE | NOT NULL | Date of the incident |
| incident_time | TIME | NOT NULL | Time of the incident |
| assigned_officer_id | UUID | NOT NULL, FOREIGN KEY | Reference to users.id |
| status | VARCHAR(20) | NOT NULL, CHECK | Current status (In Progress, Completed) |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Case creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

### 3. interviews
Stores witness interview information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique interview identifier |
| case_id | VARCHAR(50) | NOT NULL, FOREIGN KEY | Reference to cases.id |
| interviewing_officer_id | UUID | NOT NULL, FOREIGN KEY | Reference to users.id |
| witness_name | VARCHAR(255) | NOT NULL | Full name of the witness |
| witness_type | VARCHAR(50) | NOT NULL, CHECK | Type of witness (Resident, Neighbor, etc.) |
| witness_contact | VARCHAR(255) | | Contact information |
| interview_environment | VARCHAR(20) | CHECK | Interview environment (controlled, field) |
| status | VARCHAR(20) | NOT NULL, CHECK | Interview status (scheduled, in_progress, completed, cancelled) |
| start_time | TIMESTAMPTZ | | Interview start time |
| end_time | TIMESTAMPTZ | | Interview end time |
| duration_seconds | INTEGER | | Interview duration in seconds |
| recording_path | TEXT | | Path to the audio recording file |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Interview creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

### 4. transcriptions
Stores complete interview transcriptions as structured data.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique transcription identifier |
| interview_id | UUID | NOT NULL, FOREIGN KEY, UNIQUE | Reference to interviews.id |
| transcription_data | JSONB | NOT NULL | Complete transcription with speakers and segments |
| language | VARCHAR(10) | DEFAULT 'en-US' | Detected/specified language |
| confidence_score | DECIMAL(3,2) | CHECK (confidence_score >= 0 AND confidence_score <= 1) | Overall transcription confidence |
| processing_status | VARCHAR(20) | DEFAULT 'pending' | Status (pending, processing, completed, failed) |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Transcription creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

### 5. statements
Stores formal interview statements.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique statement identifier |
| interview_id | UUID | NOT NULL, FOREIGN KEY, UNIQUE | Reference to interviews.id |
| content | TEXT | NOT NULL | Formal statement content |
| officer_notes | TEXT | | Additional officer notes |
| version | INTEGER | NOT NULL, DEFAULT 1 | Statement version number |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Statement creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

### 6. audio_recordings
Stores audio recording metadata and file information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique recording identifier |
| interview_id | UUID | NOT NULL, FOREIGN KEY, UNIQUE | Reference to interviews.id |
| file_path | TEXT | NOT NULL | Path to audio file in storage |
| file_size | BIGINT | | File size in bytes |
| duration_seconds | INTEGER | | Recording duration in seconds |
| format | VARCHAR(10) | | Audio format (mp3, wav, etc.) |
| sample_rate | INTEGER | | Audio sample rate |
| channels | INTEGER | | Number of audio channels |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Recording creation timestamp |

### 7. export_logs
Tracks document exports for auditing purposes.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique export identifier |
| interview_id | UUID | NOT NULL, FOREIGN KEY | Reference to interviews.id |
| export_type | VARCHAR(10) | NOT NULL, CHECK | Export format (pdf, docx) |
| file_path | TEXT | | Path to exported file |
| exported_by_user_id | UUID | FOREIGN KEY | Reference to users.id |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Export timestamp |

## Indexes

### Performance Indexes
- `idx_users_email` on users(email)
- `idx_users_badge_number` on users(badge_number)
- `idx_cases_status` on cases(status)
- `idx_cases_assigned_officer` on cases(assigned_officer_id)
- `idx_cases_incident_date` on cases(incident_date)
- `idx_interviews_case_id` on interviews(case_id)
- `idx_interviews_officer_id` on interviews(interviewing_officer_id)
- `idx_interviews_status` on interviews(status)
- `idx_transcriptions_interview_id` on transcriptions(interview_id)
- `idx_transcriptions_status` on transcriptions(processing_status)

## JSONB Transcription Data Structure

The `transcription_data` JSONB column stores the complete transcription in the following format:

```json
{
  "speakers": [
    {
      "id": "officer",
      "name": "Detective Johnson",
      "type": "officer"
    },
    {
      "id": "witness",
      "name": "Sarah Williams",
      "type": "witness"
    }
  ],
  "segments": [
    {
      "speaker": "officer",
      "timestamp": "00:00",
      "text": "Please state your name for the record.",
      "confidence": 0.95
    },
    {
      "speaker": "witness",
      "timestamp": "00:05",
      "text": "My name is Sarah Williams.",
      "confidence": 0.98
    }
  ],
  "metadata": {
    "total_duration": 1800,
    "segment_count": 45,
    "average_confidence": 0.94
  }
}
```

## Row Level Security (RLS)

Supabase RLS policies will be implemented to ensure:
- Officers can only access cases assigned to them or interviews they conducted
- Users can only see their own user data and colleagues in same department
- Interview data is protected based on case access and interviewing officer
- Transcription data follows the same access patterns as interviews
- Export logs maintain audit trail integrity

## Storage Buckets

### audio-recordings
- Stores audio recording files
- Access controlled by RLS policies
- Automatic cleanup policies for old recordings

### exported-documents
- Stores generated PDF and Word documents
- Temporary storage with automatic cleanup
- Access controlled by RLS policies

## Triggers

### updated_at Triggers
Automatic timestamp updates for:
- cases.updated_at
- interviews.updated_at
- statements.updated_at

### Audit Triggers
- Log changes to sensitive data
- Track access patterns for compliance

## Relationships

```
users (1) ──── (many) cases [assigned_officer_id]
users (1) ──── (many) interviews [interviewing_officer_id]
users (1) ──── (many) export_logs [exported_by_user_id]
cases (1) ──── (many) interviews
interviews (1) ──── (1) transcriptions
interviews (1) ──── (1) statements
interviews (1) ──── (1) audio_recordings
interviews (1) ──── (many) export_logs
```

## Data Validation

### Check Constraints
- user.role IN ('officer', 'admin', 'supervisor')
- case.status IN ('In Progress', 'Completed')
- interview.status IN ('scheduled', 'in_progress', 'completed', 'cancelled')
- witness_type IN ('Resident', 'Neighbor', 'Passerby', 'Business Owner', 'Emergency Responder')
- interview_environment IN ('controlled', 'field')
- transcription.processing_status IN ('pending', 'processing', 'completed', 'failed')
- export_type IN ('pdf', 'docx')
- confidence_score BETWEEN 0 AND 1

### Business Rules
- end_time must be after start_time when both are set
- duration_seconds should match calculated time difference
- Only one active interview per case at a time
- Statement version increments on updates
- Transcription data must contain valid JSON structure
- Officers can only interview cases they are assigned to or have permission for
