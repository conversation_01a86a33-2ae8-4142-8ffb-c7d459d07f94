# SCDF Application Deployment Guide

This guide explains how to deploy the SCDF application using Docker and Docker Compose.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Git

## Environment Setup

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd scdf
```

### 2. Environment Configuration

#### Backend Environment
```bash
cp backend/.env.example backend/.env
```
Edit `backend/.env` with your actual values:
- `OPENAI_API_KEY`: Your OpenAI API key
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Your Supabase service role key
- `SPEECHMATICS_API_KEY`: Your Speechmatics API key

#### Frontend Environment
```bash
cp frontend/.env.example frontend/.env
```
Edit `frontend/.env` with your actual values:
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `SUPABASE_SERVICE_KEY`: Your Supabase service role key

## Deployment Options

### Production Deployment
```bash
# Build and start services in production mode
docker-compose -f docker-compose.prod.yml up -d --build

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Stop services
docker-compose -f docker-compose.prod.yml down
```

### Development Deployment
```bash
# Build and start services in development mode (with hot reload)
docker-compose -f docker-compose.dev.yml up -d --build

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

### Standard Deployment
```bash
# Build and start services using default configuration
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## Service URLs

- **Application (via Nginx)**: http://localhost:8080
- **Backend API (via Nginx)**: http://localhost:8080/api
- **WebSocket (via Nginx)**: ws://localhost:8080/ws
- **Health Check**: http://localhost:8080/health

### Direct Service Access (for debugging)
- **Frontend**: http://localhost:3000 (only in dev mode)
- **Backend API**: http://localhost:8000 (only in dev mode)

## Health Checks

Both services include health check endpoints:

### Application Health Check (via Nginx)
```bash
curl http://localhost:8080/health
```

### API Health Check (via Nginx)
```bash
curl http://localhost:8080/api/health
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure ports 3000 and 8000 are not in use
2. **Environment Variables**: Verify all required environment variables are set
3. **Docker Resources**: Ensure Docker has sufficient memory (recommended: 4GB+)

### Viewing Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f fastapi-backend
docker-compose logs -f nextjs-frontend
```

### Rebuilding Services
```bash
# Rebuild all services
docker-compose build --no-cache

# Rebuild specific service
docker-compose build --no-cache fastapi-backend
```

## Production Considerations

1. **Environment Variables**: Use secure, production-specific values
2. **SSL/TLS**: Configure reverse proxy (nginx/traefik) for HTTPS
3. **Database**: Ensure Supabase is properly configured for production
4. **Monitoring**: Set up monitoring for health check endpoints
5. **Backups**: Implement backup strategy for data
6. **Scaling**: Consider horizontal scaling for high traffic

## Security Notes

- Services run as non-root users
- Only necessary ports are exposed
- Environment variables are isolated
- Health checks are implemented for monitoring
- Resource limits are set in production configuration
