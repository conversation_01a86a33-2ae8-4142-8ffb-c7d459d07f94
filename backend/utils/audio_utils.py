import wave
import os

def save_pcm_to_wav(audio_buffer: bytearray, filename: str, sample_rate: int = 16000, nchannels: int = 1, sampwidth: int = 2):
    """
    Save raw PCM audio data to a WAV file.

    Args:
        audio_buffer (bytearray): The raw PCM audio buffer to save.
        filename (str): Path to the output WAV file.
        sample_rate (int, optional): Sample rate of the audio. Defaults to 16000.
        nchannels (int, optional): Number of audio channels. Defaults to 1 (mono).
        sampwidth (int, optional): Sample width in bytes. Defaults to 2 (16-bit audio).
    """
    with wave.open(filename, "wb") as wf:
        wf.setnchannels(nchannels)
        wf.setsampwidth(sampwidth)  # 2 for 16-bit PCM (16 bits = 2 bytes)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_buffer)

def upload_wav_to_supabase(supabase, bucket: str, storage_path: str, wav_filename: str):
    """
    Upload a WAV audio file to a Supabase storage bucket.

    Args:
        supabase: Supabase client instance.
        bucket (str): The storage bucket name.
        storage_path (str): The path (including filename) in the bucket.
        wav_filename (str): Local path to the WAV file to upload.

    Returns:
        dict: The response from the Supabase upload operation.
    """
    with open(wav_filename, "rb") as f:
        response = (
            supabase.storage
            .from_(bucket)
            .upload(
                file=f,
                path=storage_path,
                file_options={
                    "content-type": "audio/wav",
                    "cache-control": "3600",
                    "upsert": "false"
                }
            )
        )
    return response

def delete_temp_file(filename: str):
    """
    Delete a temporary file from the filesystem.

    Args:
        filename (str): The path to the file to delete.
    """
    try:
        os.remove(filename)
    except Exception as e:
        print(f"[ERROR] Failed to delete temp file: {filename}. Reason: {e}")
