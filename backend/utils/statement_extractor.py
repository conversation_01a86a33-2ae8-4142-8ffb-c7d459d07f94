import os
import json
import openai
import asyncio
import ast
from dotenv import load_dotenv

load_dotenv()
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")

# Field contexts to guide the LLM extraction
field_contexts = """
Field Contexts:
- full_name: Full name of the person giving the statement, including aliases if mentioned.
- aliases: Any nicknames or aliases of the person, if mentioned.
- guardian_name: Full name of the guardian (if applicable).
- guardian_id_number: NRIC, FIN, or passport number of the guardian (if applicable).
- gender: Gender of the person giving the statement.
- age: Age of the person giving the statement.
- date_of_birth: Date of birth of the person giving the statement.
- marital_status: Marital status (e.g., Single, Married, Divorced).
- id_number: NRIC, FIN, passport, work permit, or employment pass number of the person.
- nationality: Nationality of the person giving the statement.
- residential_address: Home address of the person giving the statement.
- occupation: Occupation or job title.
- employment_address: Name of company/organization and/or work address.
- email_address: Email address.
- phone_number: Main contact phone number.
- guardian_phone_number: Contact phone number of guardian (if any).
- languages_spoken: Languages spoken by the person (list if more than one).
"""

example_transcript = """
IO: Can you state your full name for the record?
Witness: My name is <PERSON> <PERSON> <PERSON>, but friends call me Ah <PERSON>.
IO: What is your NRIC?
Witness: *********.
IO: How old are you, and what is your date of birth?
Witness: I am 28 years old, born 16 July 1996.
IO: What is your gender?
Witness: Male.
IO: What's your marital status?
Witness: Single.
IO: Where do you live?
Witness: 123 Yishun Ave 4, #03-12, Singapore 760123.
IO: What is your nationality?
Witness: Singaporean.
IO: What language do you speak at home?
Witness: English and Mandarin.
IO: What is your occupation and where do you work?
Witness: I am a software engineer at GovTech, 10 Pasir Panjang Road, #10-01.
IO: What's your email address and phone number?
Witness: <EMAIL>, 91234567.
IO: Who is your guardian?
Witness: My father, Tan Kim Seng, *********, his contact is 98765432.
"""

example_json = {
    "full_name": "Tan Wei Ming",
    "aliases": ["Ah Ming"],
    "guardian_name": "Tan Kim Seng",
    "guardian_id_number": "*********",
    "gender": "Male",
    "age": 28,
    "date_of_birth": "1996-07-16",
    "marital_status": "Single",
    "id_number": "*********",
    "nationality": "Singaporean",
    "residential_address": "123 Yishun Ave 4, #03-12, Singapore 760123",
    "occupation": "Software engineer",
    "employment_address": "GovTech, 10 Pasir Panjang Road, #10-01",
    "email_address": "<EMAIL>",
    "phone_number": "91234567",
    "guardian_phone_number": "98765432",
    "languages_spoken": ["English", "Mandarin"]
}

async def extract_statement_info(segments):
    """
    Use OpenAI to extract key statement info fields from transcript segments.
    """
    full_transcript = ""
    for segment in segments:
        speaker = segment.get("speaker", "Unknown")
        text = segment.get("text", "")
        full_transcript += f"{speaker}: {text}\n"

    prompt = f"""
Extract the following information from the transcript as a JSON object. 
If a field is missing or not mentioned, use null (or an empty list for languages_spoken).

{field_contexts}

Example transcript:
{example_transcript}

Expected JSON:
{json.dumps(example_json, ensure_ascii=False, indent=2)}

Now extract the fields for the following transcript:

\"\"\"{full_transcript}\"\"\"

Return only the JSON object, no explanation or extra text.
"""

    if not OPENAI_API_KEY:
        return {
            "full_name": None,
            "aliases": [],
            "guardian_name": None,
            "guardian_id_number": None,
            "gender": None,
            "age": None,
            "date_of_birth": None,
            "marital_status": None,
            "id_number": None,
            "nationality": None,
            "residential_address": None,
            "occupation": None,
            "employment_address": None,
            "email_address": None,
            "phone_number": None,
            "guardian_phone_number": None,
            "languages_spoken": []
        }

    try:
        client = openai.OpenAI(api_key=OPENAI_API_KEY)
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "Extract and return only a JSON object with the specified fields, do not explain.",
                    },
                    {
                        "role": "user",
                        "content": prompt,
                    },
                ],
                max_tokens=700,
                temperature=0.2,
            ),
        )
        content = response.choices[0].message.content.strip()
        if content.startswith("```json"):
            content = content[7:]
        if content.startswith("```"):
            content = content[3:]
        if content.endswith("```"):
            content = content[:-3]
        try:
            statement_info = json.loads(content)
        except Exception:
            statement_info = ast.literal_eval(content)
        return statement_info
    except Exception as e:
        print(f"LLM extraction failed: {e}")
        return None
